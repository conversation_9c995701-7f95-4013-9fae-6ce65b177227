"""
Configuration for LangMem memory management.
"""
import os
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

# Qdrant configuration
QDRANT_URL = os.getenv("QDRANT_URL", "http://74.208.122.216:6333")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME", "memories_tgpt")

# Memory configuration
MAX_HISTORY_ITEMS = 5  # Number of conversation history items to keep
MAX_MEMORIES_PER_CHAT = 15  # Maximum number of memories to store per chat_id (sliding window)
MEMORY_EMBEDDING_MODEL = "text-embedding-3-small"  # OpenAI embedding model
MEMORY_RELEVANCE_THRESHOLD = 0.75  # Threshold for memory relevance
LLM_MODEL = "gpt-4o-mini"  # Model to use for memory extraction and compression

# Order-based retrieval configuration
ENABLE_ORDER_RANKING = True  # Enable/disable order-based scoring (most recent preferred)
ORDER_WEIGHT_FACTOR = 0.5  # How much to weight recency vs semantic similarity (0.0 = only semantic, 1.0 = only order)

# Memory extraction instructions
MEMORY_EXTRACTION_INSTRUCTIONS = """
Extract all noteworthy facts, events, and relationships from the conversation.
Focus on:
1. User preferences and interests
2. Important facts mentioned by the user
3. User's investment style and risk tolerance
4. Specific stocks or financial instruments the user is interested in
5. User's goals and objectives

For each memory, indicate its importance with [IMPORTANT] for critical information
or [BACKGROUND] for contextual information.
"""

# Memory schemas
class UserProfile(BaseModel):
    """User profile model for storing user preferences and information."""
    name: Optional[str] = None
    preferred_name: Optional[str] = None
    response_style_preference: Optional[str] = None
    special_skills: List[str] = []
    investment_preferences: List[str] = []
    risk_tolerance: Optional[str] = None
    preferred_stocks: List[str] = []
    other_preferences: List[str] = []

class Episode(BaseModel):
    """An episode captures how to handle a specific situation."""
    observation: str
    thoughts: str
    action: str
    result: str

# Memory namespace configuration
# Format: (organization, user_id_template, context)
MEMORY_NAMESPACE = ("tradergpt", "{user_id}", "financial_assistant")

# Background processing configuration
BACKGROUND_PROCESSING = True  # Whether to process memories in the background
COMPRESSION_THRESHOLD = 2  # Number of messages before compression
