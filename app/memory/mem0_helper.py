import asyncio
from typing import Dict, List, Any, Optional

# try:
#     from mem0 import Memory
# except ImportError:
#     Memory = None
#     logger.warning("Mem0 not available - Memory class not found")

from app.logging_custom_directory.logger_custom import logger
from app.memory.mem0_config import DEFAULT_SEARCH_LIMIT, MEMORY_RELEVANCE_THRESHOLD, create_mem0_instance

class Mem0Helper:
    """
    Simple helper class for mem0 operations in generate_reply.py
    """

    def __init__(self):
            """Initialize the mem0 memory manager."""
            try:
                # Initialize mem0 instance
                self.memory = create_mem0_instance()
                logger.info("Mem0 memory manager initialized successfully")

                # Verify Qdrant collection exists
                self._verify_qdrant_collection()
            except Exception as e:
                logger.error(f"Error initializing mem0 memory manager: {e}")
                raise

    def _verify_qdrant_collection(self):
        """
        Verify that the Qdrant collection exists and is properly configured.
        If not, attempt to create it.
        """
        try:
            # Try to get collection info from the vector store
            vector_store = self.memory.vector_store
            collection_name = vector_store.collection_name

            # Check if collection exists by trying to get its info
            try:
                collection_info = vector_store.client.get_collection(collection_name)
                logger.info(f"Qdrant collection '{collection_name}' exists: {collection_info}")
            except Exception as e:
                logger.warning(f"Qdrant collection '{collection_name}' does not exist: {e}")

                # Try to create the collection
                try:
                    logger.info(f"Attempting to create Qdrant collection '{collection_name}'")
                    vector_size = 1536  # OpenAI embedding size
                    vector_store.client.create_collection(
                        collection_name=collection_name,
                        vectors_config={"size": vector_size, "distance": "Cosine"}
                    )
                    logger.info(f"Successfully created Qdrant collection '{collection_name}'")
                except Exception as create_error:
                    logger.error(f"Failed to create Qdrant collection: {create_error}")
        except Exception as e:
            logger.error(f"Error verifying Qdrant collection: {e}")

    def is_available(self) -> bool:
        """Check if mem0 is available and initialized."""
        return self.memory is not None
    
    async def search_memories(
        self,
        user_id: str,
        query: str,
        chat_id: Optional[str] = None,
        limit: int = DEFAULT_SEARCH_LIMIT
    ) -> List[Dict[str, Any]]:
        """
        Search memories using mem0.

        Args:
            user_id: The user ID
            query: Search query
            chat_id: Optional chat ID for scoped search
            limit: Maximum number of results

        Returns:
            List of memory dictionaries
        """
        if not self.is_available():
            logger.warning("Mem0 not available for search")
            return []

        try:
            # Create user identifier (include chat_id for scoped search)
            mem0_user_id = user_id

            # Search using mem0
            search_results = self.memory.search(
                query=query,
                user_id=mem0_user_id,
                limit=limit
            )

            # Format results
            formatted_results = []
            for result in search_results:
                memory_content = result.get("memory", "")
                metadata = result.get("metadata", {})
                score = result.get("score", 0.0)

                # Apply relevance threshold
                #if score >= MEMORY_RELEVANCE_THRESHOLD:
                formatted_results.append({
                    "memory": memory_content,
                    "score": score,
                    "metadata": metadata,
                    "source": "mem0"
                })

            logger.info(f"Mem0 search found {len(formatted_results)} relevant memories for user {user_id}")
            return formatted_results

        except Exception as e:
            logger.error(f"Error searching memories with mem0: {e}")
            return []

    async def store_memory(
        self,
        user_id: str,
        messages: List[Dict[str, Any]],
        chat_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store memory using mem0.

        Args:
            user_id: The user ID
            messages: List of message dictionaries
            chat_id: Optional chat ID
            metadata: Optional metadata

        Returns:
            Success status
        """
        if not self.is_available():
            logger.warning("Mem0 not available for storage")
            return False

        try:
            # Create user identifier
            mem0_user_id = user_id

            # Format conversation content
            conversation_parts = []
            for msg in messages:
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    role = msg["role"]
                    content = msg["content"]
                    if role == "user":
                        conversation_parts.append(f"User: {content}")
                    elif role == "assistant":
                        conversation_parts.append(f"Assistant: {content}")

            conversation_content = "\n".join(conversation_parts)

            if not conversation_content:
                logger.warning("No conversation content to store")
                return False

            # Prepare metadata
            store_metadata = {
                "user_id": user_id,
                "source": "tradergpt",
                "memory_type": "conversation"
            }

            if chat_id:
                store_metadata["chat_id"] = chat_id

            if metadata:
                store_metadata.update(metadata)

            # Store using mem0
            result = self.memory.add(
                messages=conversation_content,
                user_id=mem0_user_id,
                metadata=store_metadata
            )

            if result:
                logger.info(f"Successfully stored memory using mem0 for user {user_id}, chat {chat_id}")
                return True
            else:
                logger.error(f"Failed to store memory using mem0 for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Error storing memory with mem0: {e}")
            return False

# Global mem0 helper instance
mem0_helper = Mem0Helper()

# Convenience functions for direct usage
async def search_mem0_memories(
    user_id: str,
    query: str,
    chat_id: Optional[str] = None,
    limit: int = DEFAULT_SEARCH_LIMIT
) -> List[Dict[str, Any]]:
    """
    Search memories using mem0 - convenience function.
    """
    return await mem0_helper.search_memories(user_id, query, chat_id, limit)

async def store_mem0_memory(
    user_id: str,
    messages: List[Dict[str, Any]],
    chat_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Store memory using mem0 - convenience function.
    """
    return await mem0_helper.store_memory(user_id, messages, chat_id, metadata)

def is_mem0_available() -> bool:
    """
    Check if mem0 is available.
    """
    return mem0_helper.is_available()

def format_mem0_context(memories: List[Dict[str, Any]], include_header: bool = True) -> str:
    """
    Format mem0 memories into context string.

    Args:
        memories: List of memory dictionaries from mem0
        include_header: Whether to include the header

    Returns:
        Formatted context string
    """
    if not memories:
        return ""

    context_parts = []
    if include_header:
        context_parts.append("\n\nRelevant memories from mem0:")

    for i, memory in enumerate(memories, 1):
        memory_content = memory.get("memory", "")
        score = memory.get("score", 0.0)

        # Truncate long memories
        if len(memory_content) > 200:
            memory_content = memory_content[:200] + "..."

        context_parts.append(f"{i}. {memory_content} (relevance: {score:.2f})")

    return "\n".join(context_parts)
